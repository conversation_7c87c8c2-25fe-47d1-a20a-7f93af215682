#!/usr/bin/env node

/**
 * Black-G CLI Comprehensive Testing Framework
 * Tests for tool discovery, command execution, AI integration, and ASM analysis
 */

const assert = require('assert');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Import the classes we want to test
// Note: In a real implementation, these would be properly exported from the main file
class TestRunner {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        
        this.colors = {
            reset: '\x1b[0m',
            green: '\x1b[32m',
            red: '\x1b[31m',
            yellow: '\x1b[33m',
            cyan: '\x1b[36m'
        };
    }

    async runAllTests() {
        console.log(`${this.colors.cyan}🧪 Black-G CLI Testing Framework${this.colors.reset}\n`);
        
        // Test categories
        await this.testToolDiscovery();
        await this.testCommandValidation();
        await this.testASMAnalysis();
        await this.testAIIntegration();
        await this.testReportGeneration();
        
        this.printSummary();
    }

    async testToolDiscovery() {
        console.log(`${this.colors.yellow}📋 Testing Tool Discovery Framework...${this.colors.reset}`);
        
        // Test 1: Tool path discovery
        await this.runTest('Tool Path Discovery', async () => {
            const result = await this.executeCommand('which nmap');
            assert(result.exitCode === 0, 'nmap should be discoverable');
            assert(result.output.includes('/usr/bin/nmap') || result.output.includes('/bin/nmap'), 'nmap path should be valid');
        });

        // Test 2: Multiple tool availability
        await this.runTest('Multiple Tool Availability', async () => {
            const tools = ['nmap', 'subfinder', 'nuclei', 'sslscan'];
            const results = await Promise.all(
                tools.map(tool => this.executeCommand(`which ${tool}`))
            );
            
            const availableTools = results.filter(r => r.exitCode === 0).length;
            assert(availableTools >= 2, `At least 2 tools should be available, found ${availableTools}`);
        });

        // Test 3: Tool execution validation
        await this.runTest('Tool Execution Validation', async () => {
            const result = await this.executeCommand('nmap --version');
            assert(result.exitCode === 0, 'nmap should execute successfully');
            assert(result.output.includes('Nmap version'), 'nmap should return version information');
        });
    }

    async testCommandValidation() {
        console.log(`${this.colors.yellow}📋 Testing Command Validation...${this.colors.reset}`);
        
        // Test 1: Valid command syntax
        await this.runTest('Valid Command Syntax', async () => {
            const validCommands = [
                'nmap -sS 127.0.0.1',
                'subfinder -d example.com',
                'nuclei -u https://example.com'
            ];
            
            for (const cmd of validCommands) {
                const isValid = !this.isCommandSyntaxInvalid(cmd);
                assert(isValid, `Command should be valid: ${cmd}`);
            }
        });

        // Test 2: Invalid command syntax detection
        await this.runTest('Invalid Command Syntax Detection', async () => {
            const invalidCommands = [
                'nmap -sS 127.0.0.1 && rm -rf /',
                'subfinder -d example.com; cat /etc/passwd',
                'nuclei -u $(whoami)'
            ];
            
            for (const cmd of invalidCommands) {
                const isInvalid = this.isCommandSyntaxInvalid(cmd);
                assert(isInvalid, `Command should be invalid: ${cmd}`);
            }
        });

        // Test 3: Risk assessment
        await this.runTest('Command Risk Assessment', async () => {
            const riskTests = [
                { cmd: 'subfinder -d example.com', expectedRisk: 'LOW' },
                { cmd: 'nmap -sS 127.0.0.1', expectedRisk: 'HIGH' },
                { cmd: 'sslscan example.com', expectedRisk: 'MEDIUM' }
            ];
            
            for (const test of riskTests) {
                const risk = this.assessSingleCommandRisk(test.cmd);
                assert(risk === test.expectedRisk, `Risk for "${test.cmd}" should be ${test.expectedRisk}, got ${risk}`);
            }
        });
    }

    async testASMAnalysis() {
        console.log(`${this.colors.yellow}📋 Testing ASM Analysis Framework...${this.colors.reset}`);
        
        // Test 1: Category classification
        await this.runTest('ASM Category Classification', async () => {
            const testResults = [
                { command: 'nmap -sS example.com', expectedCategory: 'Open Ports & Services' },
                { command: 'sslscan example.com', expectedCategory: 'SSL/TLS Certificate Analysis' },
                { command: 'subfinder -d example.com', expectedCategory: 'Domain/IP Vulnerabilities' }
            ];
            
            for (const test of testResults) {
                const category = this.categorizeResult({ command: test.command });
                assert(category === test.expectedCategory, 
                    `Command "${test.command}" should be categorized as "${test.expectedCategory}", got "${category}"`);
            }
        });

        // Test 2: Risk scoring
        await this.runTest('Risk Scoring Algorithm', async () => {
            const mockFindings = [
                { type: 'open_port', port: '22', service: 'ssh', severity: 'MEDIUM' },
                { type: 'vulnerability', description: 'Critical RCE found', severity: 'CRITICAL' },
                { type: 'ssl_vulnerability', description: 'Weak cipher', severity: 'HIGH' }
            ];
            
            const riskScore = this.calculateMockRiskScore(mockFindings);
            assert(riskScore >= 7, `Risk score should be high for critical findings, got ${riskScore}`);
        });

        // Test 3: Report generation structure
        await this.runTest('Report Structure Validation', async () => {
            const mockResults = [
                { command: 'nmap -sS example.com', status: 'SUCCESS', result: { output: 'Port 80 open' } },
                { command: 'sslscan example.com', status: 'SUCCESS', result: { output: 'TLS 1.2 supported' } }
            ];
            
            const analysis = this.generateMockAnalysis(mockResults, 'example.com');
            
            assert(analysis.target === 'example.com', 'Analysis should include target');
            assert(analysis.timestamp, 'Analysis should include timestamp');
            assert(analysis.categories, 'Analysis should include categories');
            assert(analysis.overallRisk, 'Analysis should include overall risk');
        });
    }

    async testAIIntegration() {
        console.log(`${this.colors.yellow}📋 Testing AI Integration...${this.colors.reset}`);
        
        // Test 1: Response parsing
        await this.runTest('AI Response Parsing', async () => {
            const mockResponse = `
🎯 OBJECTIVE: Test objective
📊 STRATEGY: Test strategy
🔍 EXECUTION_PLAN: Test execution plan
⚠️ RISK_ASSESSMENT: Test risk assessment
📋 EXPECTED_OUTCOMES: Test outcomes

COMMAND: nmap -sS example.com
DESCRIPTION: Port scan
CATEGORY: Open Ports & Services
            `;
            
            const sections = this.parseAIResponse(mockResponse);
            assert(sections.objective === 'Test objective', 'Should parse objective correctly');
            assert(sections.strategy === 'Test strategy', 'Should parse strategy correctly');
            
            const commands = this.extractCommands(mockResponse);
            assert(commands.length === 1, 'Should extract one command');
            assert(commands[0].command === 'nmap -sS example.com', 'Should extract command correctly');
        });

        // Test 2: Context preparation
        await this.runTest('Context Preparation', async () => {
            const baseInput = 'scan example.com';
            const contextualInput = this.prepareContextualInput(baseInput);
            
            assert(contextualInput.includes('TOOL_STATUS'), 'Should include tool status');
            assert(contextualInput.length > baseInput.length, 'Should add contextual information');
        });
    }

    async testReportGeneration() {
        console.log(`${this.colors.yellow}📋 Testing Report Generation...${this.colors.reset}`);
        
        // Test 1: JSON report structure
        await this.runTest('JSON Report Structure', async () => {
            const mockSession = {
                id: 'test-session',
                target: 'example.com',
                results: [
                    { command: 'nmap -sS example.com', status: 'SUCCESS', result: { output: 'Port 80 open' } }
                ]
            };
            
            const jsonReport = this.generateMockJSONReport(mockSession);
            const parsed = JSON.parse(jsonReport);
            
            assert(parsed.session_id === 'test-session', 'Should include session ID');
            assert(parsed.target === 'example.com', 'Should include target');
            assert(parsed.results.length === 1, 'Should include results');
        });

        // Test 2: Markdown report generation
        await this.runTest('Markdown Report Generation', async () => {
            const mockSession = {
                id: 'test-session',
                target: 'example.com',
                results: [
                    { command: 'nmap -sS example.com', status: 'SUCCESS', result: { output: 'Port 80 open' } }
                ]
            };
            
            const markdownReport = this.generateMockMarkdownReport(mockSession);
            
            assert(markdownReport.includes('# Black-G Security Assessment Report'), 'Should have proper title');
            assert(markdownReport.includes('example.com'), 'Should include target');
            assert(markdownReport.includes('nmap'), 'Should include command details');
        });
    }

    async runTest(testName, testFunction) {
        this.testResults.total++;
        
        try {
            await testFunction();
            this.testResults.passed++;
            console.log(`  ${this.colors.green}✓${this.colors.reset} ${testName}`);
            this.testResults.details.push({ name: testName, status: 'PASSED' });
        } catch (error) {
            this.testResults.failed++;
            console.log(`  ${this.colors.red}✗${this.colors.reset} ${testName}: ${error.message}`);
            this.testResults.details.push({ name: testName, status: 'FAILED', error: error.message });
        }
    }

    async executeCommand(command) {
        return new Promise((resolve) => {
            const parts = command.split(' ');
            const tool = parts[0];
            const args = parts.slice(1);
            
            let output = '';
            let errorOutput = '';
            
            const child = spawn(tool, args, { stdio: ['pipe', 'pipe', 'pipe'] });
            
            child.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            child.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            
            child.on('close', (code) => {
                resolve({
                    exitCode: code,
                    output: output,
                    errorOutput: errorOutput
                });
            });
            
            child.on('error', (err) => {
                resolve({
                    exitCode: -1,
                    output: '',
                    errorOutput: err.message
                });
            });
        });
    }

    printSummary() {
        console.log(`\n${this.colors.cyan}📊 Test Summary${this.colors.reset}`);
        console.log(`Total Tests: ${this.testResults.total}`);
        console.log(`${this.colors.green}Passed: ${this.testResults.passed}${this.colors.reset}`);
        console.log(`${this.colors.red}Failed: ${this.testResults.failed}${this.colors.reset}`);
        
        const successRate = Math.round((this.testResults.passed / this.testResults.total) * 100);
        console.log(`Success Rate: ${successRate}%`);
        
        if (this.testResults.failed > 0) {
            console.log(`\n${this.colors.red}Failed Tests:${this.colors.reset}`);
            this.testResults.details
                .filter(test => test.status === 'FAILED')
                .forEach(test => {
                    console.log(`  - ${test.name}: ${test.error}`);
                });
        }
        
        console.log(`\n${successRate >= 80 ? this.colors.green + '✓' : this.colors.red + '✗'} Overall: ${successRate >= 80 ? 'PASS' : 'FAIL'}${this.colors.reset}`);
    }

    // Helper methods for testing (simplified versions of main functionality)

    isCommandSyntaxInvalid(command) {
        // Basic syntax checks for security
        if (command.includes('&&') || command.includes('||') || command.includes(';')) {
            return true; // Prevent command chaining
        }

        if (command.includes('$(') || command.includes('`')) {
            return true; // Prevent command substitution
        }

        return false;
    }

    assessSingleCommandRisk(command) {
        const highRiskTools = ['masscan', 'nmap', 'nuclei'];
        const mediumRiskTools = ['gobuster', 'sslscan'];
        const lowRiskTools = ['subfinder', 'amass', 'whois', 'dig'];

        const toolName = command.split(' ')[0];

        if (highRiskTools.includes(toolName)) return 'HIGH';
        if (mediumRiskTools.includes(toolName)) return 'MEDIUM';
        if (lowRiskTools.includes(toolName)) return 'LOW';

        return 'UNKNOWN';
    }

    categorizeResult(result) {
        const command = result.command.toLowerCase();

        const categories = {
            'Domain/IP Vulnerabilities': ['subdomain', 'dns', 'domain', 'ip', 'network'],
            'SSL/TLS Certificate Analysis': ['ssl', 'tls', 'certificate', 'cipher'],
            'Configuration Issues': ['config', 'header', 'security', 'default'],
            'Open Ports & Services': ['port', 'service', 'banner', 'version'],
            'IP/Domain Reputation': ['reputation', 'blacklist', 'threat'],
            'Cloud Security Assessment': ['cloud', 'aws', 'azure', 'gcp', 's3'],
            'Authentication Discovery': ['login', 'auth', 'password', 'credential']
        };

        for (const [categoryName, indicators] of Object.entries(categories)) {
            for (const indicator of indicators) {
                if (command.includes(indicator)) {
                    return categoryName;
                }
            }
        }

        return 'General Assessment';
    }

    calculateMockRiskScore(findings) {
        let score = 0;

        for (const finding of findings) {
            switch (finding.severity) {
                case 'CRITICAL': score += 10; break;
                case 'HIGH': score += 7; break;
                case 'MEDIUM': score += 5; break;
                case 'LOW': score += 3; break;
                default: score += 1;
            }
        }

        return score;
    }

    generateMockAnalysis(results, target) {
        return {
            target: target,
            timestamp: new Date().toISOString(),
            categories: {},
            overallRisk: 'MEDIUM',
            riskScore: 5,
            findings: [],
            recommendations: []
        };
    }

    parseAIResponse(response) {
        const sections = {};

        // Extract structured sections using regex patterns
        const patterns = {
            objective: /🎯\s*OBJECTIVE:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            strategy: /📊\s*STRATEGY:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            execution_plan: /🔍\s*EXECUTION[_\s]*PLAN:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            risk_assessment: /⚠️\s*RISK[_\s]*ASSESSMENT:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i,
            expected_outcomes: /📋\s*EXPECTED[_\s]*OUTCOMES:\s*([^\n]*(?:\n(?!🎯|📊|🔍|⚠️|📋)[^\n]*)*)/i
        };

        for (const [key, pattern] of Object.entries(patterns)) {
            const match = response.match(pattern);
            if (match) {
                sections[key] = match[1].trim();
            }
        }

        return sections;
    }

    extractCommands(response) {
        const commands = [];
        const lines = response.split('\n');

        let currentCommand = null;

        for (const line of lines) {
            const trimmedLine = line.trim();

            if (trimmedLine.startsWith('COMMAND:')) {
                if (currentCommand) {
                    commands.push(currentCommand);
                }
                currentCommand = {
                    command: trimmedLine.replace('COMMAND:', '').trim(),
                    description: '',
                    category: ''
                };
            } else if (currentCommand && trimmedLine.startsWith('DESCRIPTION:')) {
                currentCommand.description = trimmedLine.replace('DESCRIPTION:', '').trim();
            } else if (currentCommand && trimmedLine.startsWith('CATEGORY:')) {
                currentCommand.category = trimmedLine.replace('CATEGORY:', '').trim();
            }
        }

        if (currentCommand) {
            commands.push(currentCommand);
        }

        return commands;
    }

    prepareContextualInput(baseInput) {
        let contextualInput = baseInput;
        contextualInput += '\n\nTOOL_STATUS: nmap, subfinder, nuclei available';
        return contextualInput;
    }

    generateMockJSONReport(session) {
        const report = {
            session_id: session.id,
            target: session.target,
            timestamp: new Date().toISOString(),
            results: session.results,
            summary: {
                total_commands: session.results.length,
                successful_commands: session.results.filter(r => r.status === 'SUCCESS').length,
                failed_commands: session.results.filter(r => r.status === 'FAILED').length
            }
        };

        return JSON.stringify(report, null, 2);
    }

    generateMockMarkdownReport(session) {
        return `# Black-G Security Assessment Report

**Target:** ${session.target}
**Session ID:** ${session.id}
**Generated:** ${new Date().toISOString()}

## Executive Summary

This report contains the results of a security assessment performed using Black-G CLI.

## Command Results

${session.results.map((result, index) => `
### ${index + 1}. \`${result.command}\`

**Status:** ${result.status}
${result.result ? `**Output:** ${result.result.output}` : ''}

---
`).join('')}

## Recommendations

- Review all findings and prioritize remediation
- Implement security best practices
- Regular security assessments recommended
`;
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const testRunner = new TestRunner();
    testRunner.runAllTests().catch(console.error);
}

module.exports = TestRunner;
